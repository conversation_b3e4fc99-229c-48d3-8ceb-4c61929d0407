{"lockfile_version": "1", "packages": {"act@latest": {"last_modified": "2025-02-16T09:28:34Z", "resolved": "github:NixOS/nixpkgs/b1b43d32be000928cc71250ed77f4a0a5f2bc23a#act", "source": "devbox-search", "version": "0.2.74", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/qr4rayifhcjqrga4y5cdz8zxm9r3qjny-act-0.2.74", "default": true}], "store_path": "/nix/store/qr4rayifhcjqrga4y5cdz8zxm9r3qjny-act-0.2.74"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/xn5byyqpzl8ar9al6d6l7zvs6i35z72m-act-0.2.74", "default": true}], "store_path": "/nix/store/xn5byyqpzl8ar9al6d6l7zvs6i35z72m-act-0.2.74"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/7mlzpsqzx34v7ymf9nbdpsv8v7kndjcq-act-0.2.74", "default": true}], "store_path": "/nix/store/7mlzpsqzx34v7ymf9nbdpsv8v7kndjcq-act-0.2.74"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/x6vas4fnkabdq7767h7n4yvl0s38nx2w-act-0.2.74", "default": true}], "store_path": "/nix/store/x6vas4fnkabdq7767h7n4yvl0s38nx2w-act-0.2.74"}}}, "gh@latest": {"last_modified": "2025-02-13T04:03:32Z", "resolved": "github:NixOS/nixpkgs/2d55b4c1531187926c2a423f6940b3b1301399b5#gh", "source": "devbox-search", "version": "2.67.0", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/9n2f576hng76zzhx308qxjzw6g4npgnh-gh-2.67.0", "default": true}], "store_path": "/nix/store/9n2f576hng76zzhx308qxjzw6g4npgnh-gh-2.67.0"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/wbb60p6ijhrajrnaz45v66gjp5wsb503-gh-2.67.0", "default": true}], "store_path": "/nix/store/wbb60p6ijhrajrnaz45v66gjp5wsb503-gh-2.67.0"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/psbp4wp18fmnynd24yklwv30465f7wwk-gh-2.67.0", "default": true}], "store_path": "/nix/store/psbp4wp18fmnynd24yklwv30465f7wwk-gh-2.67.0"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/6zxliiicnw366ma37132lqd9hcwxs1ic-gh-2.67.0", "default": true}], "store_path": "/nix/store/6zxliiicnw366ma37132lqd9hcwxs1ic-gh-2.67.0"}}}, "git@latest": {"last_modified": "2025-02-23T09:42:26Z", "resolved": "github:NixOS/nixpkgs/2d068ae5c6516b2d04562de50a58c682540de9bf#git", "source": "devbox-search", "version": "2.48.1", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/48bd3kx9xl36ixnzax9chik2y2l7lx9m-git-2.48.1", "default": true}, {"name": "doc", "path": "/nix/store/7clrn7z9dxxl1n9hy2vy5nrk58mrb258-git-2.48.1-doc"}], "store_path": "/nix/store/48bd3kx9xl36ixnzax9chik2y2l7lx9m-git-2.48.1"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/4rfdh67qx08marb8kwl8pk98pkwl8kiv-git-2.48.1", "default": true}, {"name": "debug", "path": "/nix/store/yf283fv2zlaaaszdcfazbz1rg6d6f18q-git-2.48.1-debug"}, {"name": "doc", "path": "/nix/store/c9hzqmbywb0basggdaylpc0j3r8j0dxs-git-2.48.1-doc"}], "store_path": "/nix/store/4rfdh67qx08marb8kwl8pk98pkwl8kiv-git-2.48.1"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/siawz69ch5kb1k1vaak8gbm6w5zmkbz6-git-2.48.1", "default": true}, {"name": "doc", "path": "/nix/store/50346pq535sxngx9r0q1rf1bh0v01pyv-git-2.48.1-doc"}], "store_path": "/nix/store/siawz69ch5kb1k1vaak8gbm6w5zmkbz6-git-2.48.1"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/nj1na0qwqhpd128vr71p70hz9jyhnz5x-git-2.48.1", "default": true}, {"name": "doc", "path": "/nix/store/2g2q2z1rkc9lgy1ib2a5cgl9mrzhqbla-git-2.48.1-doc"}, {"name": "debug", "path": "/nix/store/7gz9ywl4jw0clbbghaii5xgdivxsdgk3-git-2.48.1-debug"}], "store_path": "/nix/store/nj1na0qwqhpd128vr71p70hz9jyhnz5x-git-2.48.1"}}}, "github:NixOS/nixpkgs/nixpkgs-unstable": {"resolved": "github:NixOS/nixpkgs/b62d2a95c72fb068aecd374a7262b37ed92df82b?lastModified=1741708242&narHash=sha256-cNRqdQD4sZpN7JLqxVOze4%2BWsWTmv2DGH0wNCOVwrWc%3D"}, "k9s@latest": {"last_modified": "2025-02-23T09:42:26Z", "resolved": "github:NixOS/nixpkgs/2d068ae5c6516b2d04562de50a58c682540de9bf#k9s", "source": "devbox-search", "version": "0.40.5", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/nik8n4pwpbgkmk12q2lsjnf2bk778w70-k9s-0.40.5", "default": true}], "store_path": "/nix/store/nik8n4pwpbgkmk12q2lsjnf2bk778w70-k9s-0.40.5"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/7f81z71pxyz7xr8s8m1l5zp17cq9c22m-k9s-0.40.5", "default": true}], "store_path": "/nix/store/7f81z71pxyz7xr8s8m1l5zp17cq9c22m-k9s-0.40.5"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/r7iqfz7bvx84jgyydn05g1yb24gfzgxd-k9s-0.40.5", "default": true}], "store_path": "/nix/store/r7iqfz7bvx84jgyydn05g1yb24gfzgxd-k9s-0.40.5"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/iycvjam25lspv9hfi5mqfdpfmxmj7kg3-k9s-0.40.5", "default": true}], "store_path": "/nix/store/iycvjam25lspv9hfi5mqfdpfmxmj7kg3-k9s-0.40.5"}}}, "kind@latest": {"last_modified": "2025-02-17T23:19:04Z", "resolved": "github:NixOS/nixpkgs/f0295845e58ada369322524631821b01c0db13a7#kind", "source": "devbox-search", "version": "0.27.0", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/fmh9mwr6mv8lpks10csrl97gm3rh0jh2-kind-0.27.0", "default": true}], "store_path": "/nix/store/fmh9mwr6mv8lpks10csrl97gm3rh0jh2-kind-0.27.0"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/i1jrn83fzdm2xr9wcn79jygisq3qaakw-kind-0.27.0", "default": true}], "store_path": "/nix/store/i1jrn83fzdm2xr9wcn79jygisq3qaakw-kind-0.27.0"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/qi782468mbssz4nvb13p258as6dc1ymp-kind-0.27.0", "default": true}], "store_path": "/nix/store/qi782468mbssz4nvb13p258as6dc1ymp-kind-0.27.0"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/9vbbi6g46px48k0kq1gpz069a9aw4fmz-kind-0.27.0", "default": true}], "store_path": "/nix/store/9vbbi6g46px48k0kq1gpz069a9aw4fmz-kind-0.27.0"}}}, "krew@latest": {"last_modified": "2025-02-07T11:26:36Z", "resolved": "github:NixOS/nixpkgs/d98abf5cf5914e5e4e9d57205e3af55ca90ffc1d#krew", "source": "devbox-search", "version": "0.4.4", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/72l2bmhff9z8k2lwx9zyarv9x0gxi3nc-krew-0.4.4", "default": true}], "store_path": "/nix/store/72l2bmhff9z8k2lwx9zyarv9x0gxi3nc-krew-0.4.4"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/3jzvj2ggfyxznz7jy7dqsqsjnw09vlsj-krew-0.4.4", "default": true}], "store_path": "/nix/store/3jzvj2ggfyxznz7jy7dqsqsjnw09vlsj-krew-0.4.4"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/wqriq0jr6zyqh0dw7d1l1xxix6rwcjiw-krew-0.4.4", "default": true}], "store_path": "/nix/store/wqriq0jr6zyqh0dw7d1l1xxix6rwcjiw-krew-0.4.4"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/s43ydgrhf8jzzrkssnjdfwq0b299681w-krew-0.4.4", "default": true}], "store_path": "/nix/store/s43ydgrhf8jzzrkssnjdfwq0b299681w-krew-0.4.4"}}}, "kubectl@latest": {"last_modified": "2025-02-16T00:50:12Z", "resolved": "github:NixOS/nixpkgs/1dcdd535fef84d4671129a10e7072d56dca9a4d3#kubectl", "source": "devbox-search", "version": "1.32.2", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/fdg47848h1jm48dvq34vzjx7pxipipyj-kubectl-1.32.2", "default": true}, {"name": "man", "path": "/nix/store/nwpq48a67gn7xvma4k76wa3dl5qpc9jw-kubectl-1.32.2-man", "default": true}, {"name": "convert", "path": "/nix/store/2l56gryy2lc3bxd1k28d606r23872vj1-kubectl-1.32.2-convert"}], "store_path": "/nix/store/fdg47848h1jm48dvq34vzjx7pxipipyj-kubectl-1.32.2"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/ln7cfszvc6gjjrarbv0ipf1h8yzgkl46-kubectl-1.32.2", "default": true}, {"name": "man", "path": "/nix/store/m79ivh1p7158mycrxiqslsdwqz0a2y15-kubectl-1.32.2-man", "default": true}, {"name": "convert", "path": "/nix/store/x9c450w9l5l9hdb4sqvysi6wlsg522x7-kubectl-1.32.2-convert"}], "store_path": "/nix/store/ln7cfszvc6gjjrarbv0ipf1h8yzgkl46-kubectl-1.32.2"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/b2js0qn4da3bl7xi06q5prx27gd5fch8-kubectl-1.32.2", "default": true}, {"name": "man", "path": "/nix/store/97yz99gy3hg69b6jjaqi01126ld5hgpl-kubectl-1.32.2-man", "default": true}, {"name": "convert", "path": "/nix/store/1cr7phxn688by6bgzrpr5x6pv29dzknp-kubectl-1.32.2-convert"}], "store_path": "/nix/store/b2js0qn4da3bl7xi06q5prx27gd5fch8-kubectl-1.32.2"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/d4hd150ffhnayv270mpacjkv57agyxyd-kubectl-1.32.2", "default": true}, {"name": "man", "path": "/nix/store/37p1rr7vy6kmfhih65p6lvwc1cx64z6x-kubectl-1.32.2-man", "default": true}, {"name": "convert", "path": "/nix/store/9ydig9bf3sgbsgxi06jw1128cdrpvfm1-kubectl-1.32.2-convert"}], "store_path": "/nix/store/d4hd150ffhnayv270mpacjkv57agyxyd-kubectl-1.32.2"}}}, "kubernetes-helm@latest": {"last_modified": "2025-02-28T17:02:31Z", "resolved": "github:NixOS/nixpkgs/5954d3359cc7178623da6c7fd23dc7f7504d7187#kuberne<PERSON>-helm", "source": "devbox-search", "version": "3.17.1", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/bzzy1zzckgl7sxr7jnsgf5vkskzd0qgv-kuberne<PERSON>-helm-3.17.1", "default": true}], "store_path": "/nix/store/bzzy1zzckgl7sxr7jnsgf5vkskzd0qgv-kuberne<PERSON>-helm-3.17.1"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/v0ff1hymbd0sqlw0z3vszq7z0sjdd68l-kuberne<PERSON>-helm-3.17.1", "default": true}], "store_path": "/nix/store/v0ff1hymbd0sqlw0z3vszq7z0sjdd68l-kuberne<PERSON>-helm-3.17.1"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/b643xxmzx0k3v2fl6kq9lyajj99nhrcf-kuberne<PERSON>-helm-3.17.1", "default": true}], "store_path": "/nix/store/b643xxmzx0k3v2fl6kq9lyajj99nhrcf-kuberne<PERSON>-helm-3.17.1"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/jnff4f0rfxp13bj7wld1yx6r26y4why7-kube<PERSON><PERSON>-helm-3.17.1", "default": true}], "store_path": "/nix/store/jnff4f0rfxp13bj7wld1yx6r26y4why7-kube<PERSON><PERSON>-helm-3.17.1"}}}, "kubeswitch@latest": {"last_modified": "2025-02-26T05:29:08Z", "resolved": "github:NixOS/nixpkgs/3a05eebede89661660945da1f151959900903b6a#kubeswitch", "source": "devbox-search", "version": "0.9.3", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/sdd30cl6yawwx5jm8ihxl6a3h8i73l5l-kubeswitch-0.9.3", "default": true}], "store_path": "/nix/store/sdd30cl6yawwx5jm8ihxl6a3h8i73l5l-kubeswitch-0.9.3"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/gr19xpsmncm1kiijzib5qv2gllmzdixv-kubeswitch-0.9.3", "default": true}], "store_path": "/nix/store/gr19xpsmncm1kiijzib5qv2gllmzdixv-kubeswitch-0.9.3"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/abfg9iqhqhmd7pkaspysq6kqhj4nmx8v-kubeswitch-0.9.3", "default": true}], "store_path": "/nix/store/abfg9iqhqhmd7pkaspysq6kqhj4nmx8v-kubeswitch-0.9.3"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/lw21j80fh0d8jpb2y2wil0pqwl560qi7-kubeswitch-0.9.3", "default": true}], "store_path": "/nix/store/lw21j80fh0d8jpb2y2wil0pqwl560qi7-kubeswitch-0.9.3"}}}, "nodePackages.jsonlint@latest": {"last_modified": "2025-02-23T09:42:26Z", "resolved": "github:NixOS/nixpkgs/2d068ae5c6516b2d04562de50a58c682540de9bf#nodePackages.jsonlint", "source": "devbox-search", "version": "1.6.3", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/vd60y7kic8fcyrccp7b7xqdd236k91nl-jsonlint-1.6.3", "default": true}], "store_path": "/nix/store/vd60y7kic8fcyrccp7b7xqdd236k91nl-jsonlint-1.6.3"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/332xhrnb69x83l3y7q9w1zg69lk0s7ib-jsonlint-1.6.3", "default": true}], "store_path": "/nix/store/332xhrnb69x83l3y7q9w1zg69lk0s7ib-jsonlint-1.6.3"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/fmpvz9dqpzhhk5ivqrpm21f4l88ijzb8-jsonlint-1.6.3", "default": true}], "store_path": "/nix/store/fmpvz9dqpzhhk5ivqrpm21f4l88ijzb8-jsonlint-1.6.3"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/dmivacvws5a7mip0y4w4aqsrzy9wzyaj-jsonlint-1.6.3", "default": true}], "store_path": "/nix/store/dmivacvws5a7mip0y4w4aqsrzy9wzyaj-jsonlint-1.6.3"}}}, "skaffold@latest": {"last_modified": "2025-02-26T05:29:08Z", "resolved": "github:NixOS/nixpkgs/3a05eebede89661660945da1f151959900903b6a#skaffold", "source": "devbox-search", "version": "2.14.1", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/8jql3mdpajkqcigpa6znxz2k9zvk8ann-skaffold-2.14.1", "default": true}], "store_path": "/nix/store/8jql3mdpajkqcigpa6znxz2k9zvk8ann-skaffold-2.14.1"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/1ql2gn2y04vz37h5wyf7qqlxb0frvff8-skaffold-2.14.1", "default": true}], "store_path": "/nix/store/1ql2gn2y04vz37h5wyf7qqlxb0frvff8-skaffold-2.14.1"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/q1mgcgh11sd7q50f3y2cg5ixahlh2ayn-skaffold-2.14.1", "default": true}], "store_path": "/nix/store/q1mgcgh11sd7q50f3y2cg5ixahlh2ayn-skaffold-2.14.1"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/3j28jvb4j09vf2dvwy625lvzvs9skimg-skaffold-2.14.1", "default": true}], "store_path": "/nix/store/3j28jvb4j09vf2dvwy625lvzvs9skimg-skaffold-2.14.1"}}}, "yamlfmt@latest": {"last_modified": "2025-02-12T00:10:52Z", "resolved": "github:NixOS/nixpkgs/83a2581c81ff5b06f7c1a4e7cc736a455dfcf7b4#yamlfmt", "source": "devbox-search", "version": "0.16.0", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/znrz9s28gwj47y6yqg9hrb50giis0awp-yamlfmt-0.16.0", "default": true}], "store_path": "/nix/store/znrz9s28gwj47y6yqg9hrb50giis0awp-yamlfmt-0.16.0"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/ca3f4m39dfqpwci3y81kk7z6xj01l2q3-yamlfmt-0.16.0", "default": true}], "store_path": "/nix/store/ca3f4m39dfqpwci3y81kk7z6xj01l2q3-yamlfmt-0.16.0"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/5rghjccvzn2ci6z0ikfmn05fjfjr3wzq-yamlfmt-0.16.0", "default": true}], "store_path": "/nix/store/5rghjccvzn2ci6z0ikfmn05fjfjr3wzq-yamlfmt-0.16.0"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/3mq7n96lga4bb6p4sr08y9f7hqyvadl5-yamlfmt-0.16.0", "default": true}], "store_path": "/nix/store/3mq7n96lga4bb6p4sr08y9f7hqyvadl5-yamlfmt-0.16.0"}}}, "yamllint@latest": {"last_modified": "2025-02-07T11:26:36Z", "resolved": "github:NixOS/nixpkgs/d98abf5cf5914e5e4e9d57205e3af55ca90ffc1d#yamllint", "source": "devbox-search", "version": "1.35.1", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/1ypz85j177gjwkq2k4rqydqsn8zj0sxq-python3.12-yamllint-1.35.1", "default": true}, {"name": "dist", "path": "/nix/store/xqik7njx28sab9m0cqk0vv7s9df13nsa-python3.12-yamllint-1.35.1-dist"}], "store_path": "/nix/store/1ypz85j177gjwkq2k4rqydqsn8zj0sxq-python3.12-yamllint-1.35.1"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/dhqg2d72rf6lj95q3rl22dsjw7vqlh3s-python3.12-yamllint-1.35.1", "default": true}, {"name": "dist", "path": "/nix/store/d7gba3vhgxy8ylgfs1rpm75wmlbyyjj2-python3.12-yamllint-1.35.1-dist"}], "store_path": "/nix/store/dhqg2d72rf6lj95q3rl22dsjw7vqlh3s-python3.12-yamllint-1.35.1"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/f3ckklymm29gagpn90nrrrmmhah3i5zc-python3.12-yamllint-1.35.1", "default": true}, {"name": "dist", "path": "/nix/store/vvmygr48vaddnk9pqfdsw7y5bc67p67q-python3.12-yamllint-1.35.1-dist"}], "store_path": "/nix/store/f3ckklymm29gagpn90nrrrmmhah3i5zc-python3.12-yamllint-1.35.1"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/7m7kphan7wjanirlyfp78lkbhpbf7i2l-python3.12-yamllint-1.35.1", "default": true}, {"name": "dist", "path": "/nix/store/6kpzvm6id8avxa3yvc7lyjgw4blw2ns4-python3.12-yamllint-1.35.1-dist"}], "store_path": "/nix/store/7m7kphan7wjanirlyfp78lkbhpbf7i2l-python3.12-yamllint-1.35.1"}}}}}