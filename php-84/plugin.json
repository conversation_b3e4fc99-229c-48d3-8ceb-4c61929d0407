{"$schema": "https://raw.githubusercontent.com/jetify-com/devbox/0.13.7/.schema/devbox-plugin.schema.json", "name": "tedatech-php-84", "version": "0.0.1", "description": "Plugin for TeDa Tech platform PHP 8.4 based projects.", "packages": ["php84@latest", "php84Extensions.xdebug@latest", "php84Extensions.intl@latest", "php84Extensions.bcmath@latest", "php84Extensions.mysqli@latest", "php84Extensions.amqp@latest", "php84Extensions.opentelemetry@latest"], "env": {"PHP_CS_FIXER_IGNORE_ENV": "1", "PHPRC": "{{ .DevboxDirRoot }}/php/php.ini", "PHP_COMPOSER_INSTALL_ARGS": "--prefer-dist --no-scripts --no-progress --no-interaction"}, "create_files": {"{{ .DevboxDirRoot }}/php/php.ini": "devbox.d/php/php.ini"}, "shell": {"scripts": {"php:ensure-composer-installed": ["test -f vendor/bin/php-cs-fixer || composer install ${PHP_COMPOSER_INSTALL_ARGS}"], "lint:php": ["devbox run php:ensure-composer-installed", "vendor/bin/php-cs-fixer check --diff"], "format:php": ["devbox run php:ensure-composer-installed", "vendor/bin/php-cs-fixer fix"]}}}