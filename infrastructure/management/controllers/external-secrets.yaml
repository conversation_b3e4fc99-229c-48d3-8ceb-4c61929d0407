apiVersion: v1
kind: Namespace
metadata:
  name: external-secrets
---
apiVersion: source.toolkit.fluxcd.io/v1
kind: HelmRepository
metadata:
  name: external-secrets
  namespace: external-secrets
spec:
  interval: 24h
  url: https://charts.external-secrets.io
---
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: external-secrets
  namespace: external-secrets
spec:
  interval: 24h
  timeout: 5m
  chart:
    spec:
      chart: external-secrets
      version: '0.14.3'
      sourceRef:
        kind: HelmRepository
        name: external-secrets
      interval: 24h
  driftDetection:
    mode: enabled
  install:
    crds: CreateReplace
    remediation:
      retries: -1
  upgrade:
    remediation:
      retries: -1
  values:
    installCRDs: true
