apiVersion: v1
kind: Namespace
metadata:
  name: velero
---
apiVersion: source.toolkit.fluxcd.io/v1
kind: HelmRepository
metadata:
  name: vmware-tanzu
  namespace: velero
spec:
  interval: 24h
  url: https://vmware-tanzu.github.io/helm-charts
---
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: velero
  namespace: velero
spec:
  interval: 24h
  timeout: 5m
  chart:
    spec:
      chart: velero
      version: '8.7.1'
      sourceRef:
        kind: HelmRepository
        name: vmware-tanzu
      interval: 24h
  driftDetection:
    mode: enabled
  install:
    crds: CreateReplace
    remediation:
      retries: -1
  upgrade:
    remediation:
      retries: -1
  values:
    deployNodeAgent: true
    initContainers:
      - name: velero-plugin-for-aws
        image: velero/velero-plugin-for-aws:v1.12.0
        imagePullPolicy: IfNotPresent
        volumeMounts:
          - mountPath: /target
            name: plugins
    configuration:
      backupStorageLocation:
        - name: default
          provider: aws
          bucket: tedatech-infra-backups
          accessMode: ReadWrite
          credential:
            name: velero-secrets
            key: cloud
          config:
            region: us-west-002
            s3ForcePathStyle: true
            s3Url: https://s3.us-west-002.backblazeb2.com
            checksumAlgorithm: ""
      volumeSnapshotLocation:
        - name: default
          provider: aws
          bucket: tedatech-infra-snapshots
          accessMode: ReadWrite
          credential:
            name: velero-secrets
            key: cloud
          config:
            region: us-west-002
            profile: default