# Schema: https://json.schemastore.org/github-action.json
name: release
on:
  push:
    branches:
      - main

jobs:
  test:
    runs-on: ubuntu-24.04
    permissions:
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: jetify-com/devbox-install-action@v0.12.0
      - run: devbox run test

  release:
    runs-on: ubuntu-24.04
    needs:
      - test
    permissions:
      contents: write
      issues: write
      packages: write
      pull-requests: write
    steps:
      - uses: actions/checkout@v4
      - id: semantic-release-tags
        uses: cycjimmy/semantic-release-action@v4
        env:
          GITHUB_TOKEN: ${{ "{{" }} secrets.GITHUB_TOKEN {{ "}}" }}
        with:
          dry_run: true
      - uses: docker/login-action@v3
        if: ${{ "{{" }} steps.semantic-release-tags.outputs.new_release_version != '' {{ "}}" }}
        with:
          registry: ghcr.io
          username: ${{ "{{" }} github.actor {{ "}}" }}
          password: ${{ "{{" }} secrets.GITHUB_TOKEN {{ "}}" }}
      - uses: hiberbee/github-action-skaffold@1.27.0
        with:
          command: build
          push: true
          tag: ${{ "{{" }} steps.semantic-release-tags.outputs.new_release_version {{ "}}" }}
          skaffold-version: "2.14.2"
      - uses: cycjimmy/semantic-release-action@v4
        env:
          GITHUB_TOKEN: ${{ "{{" }} secrets.GITHUB_TOKEN {{ "}}" }}
