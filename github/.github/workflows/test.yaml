# Schema: https://json.schemastore.org/github-action.json
name: test
on:
  pull_request:
    branches:
      - main

permissions:
  contents: read
  pull-requests: write
  checks: write

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/setup
        with:
          with-npm-cache: 'true'
          with-services: 'true'
      - run: devbox run test

  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/setup
      - run: devbox run lint
