{"$schema": "https://raw.githubusercontent.com/jetify-com/devbox/0.13.7/.schema/devbox-plugin.schema.json", "name": "te<PERSON><PERSON>-github", "version": "0.0.1", "description": "Plugin for TeDa Tech platform GitHub based projects.", "packages": ["act@latest"], "create_files": {".github/setup/action.yaml": ".github/setup/action.yaml", ".github/workflows/test.yaml": ".github/workflows/test.yaml", ".github/workflows/release.yaml": ".github/workflows/release.yaml", "release.config.mjs": "release.config.mjs"}}